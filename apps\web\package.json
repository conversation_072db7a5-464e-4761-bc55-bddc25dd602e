{"name": "@ninebot/web", "version": "0.0.0", "private": true, "author": "Peter.xu <<EMAIL>>", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "codegen:dev": "NODE_TLS_REJECT_UNAUTHORIZED=0 graphql-codegen --require dotenv/config --config codegen.js dotenv_config_path=.env.development", "codegen:prod": "NODE_TLS_REJECT_UNAUTHORIZED=0 graphql-codegen --require dotenv/config --config codegen.js dotenv_config_path=.env.production", "deploy:master": "pnpm i && pnpm next info && pnpm codegen:prod && pnpm build && ENV_FILE=.env.production PM2_HOME=./deploy/.pm2 pm2 start deploy/ecosystem.config.js", "deploy:slave": "pnpm i && pnpm next info && ENV_FILE=.env.production PM2_HOME=./deploy/.pm2 pm2 start deploy/ecosystem.config.js", "pm2:delete:all": "PM2_HOME=./deploy/.pm2 pnpm pm2 delete all", "pm2:list": "PM2_HOME=./deploy/.pm2 pnpm pm2 list"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.1", "@ninebot/core": "workspace:*", "@splidejs/react-splide": "^0.7.12", "antd": "^5.22.1", "react-player": "^3.3.1"}, "devDependencies": {"@ninebot/eslint-config": "workspace:*", "@ninebot/tailwind-config": "workspace:*", "dotenv": "^16.5.0"}}