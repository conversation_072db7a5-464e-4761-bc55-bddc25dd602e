'use client'

import { useEffect, useRef, useState } from 'react'
import ReactPlayer from 'react-player'

import { Play } from '@/components'

// 扩展 Document 和 HTMLElement 接口以支持全屏 API
declare global {
  interface Document {
    webkitExitFullscreen?: () => Promise<void>
    msExitFullscreen?: () => Promise<void>
  }

  interface HTMLElement {
    webkitRequestFullscreen?: () => Promise<void>
    msRequestFullscreen?: () => Promise<void>
  }
}

//图标
const IconVolumeMute = () => (
  <svg width={24} height={24} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path opacity="0.01" d="M16.125 6.75H11.25V11.625H16.125V6.75Z" fill="#fff" />
    <path
      d="M16.6432 11.0008L12.0007 6.3583"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.6016 6.3999L12.0423 10.9592"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 2.25V15.75C6.375 15.75 4.42444 12.3147 4.42444 12.3147H2.25C1.83579 12.3147 1.5 11.9789 1.5 11.5647V6.37905C1.5 5.96482 1.83579 5.62905 2.25 5.62905H4.42444C4.42444 5.62905 6.375 2.25 9 2.25Z"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinejoin="round"
    />
  </svg>
)
const IconVolumeUnmute = () => (
  <svg width={24} height={24} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9 2.25V15.75C6.375 15.75 4.42444 12.3147 4.42444 12.3147H2.25C1.83579 12.3147 1.5 11.9789 1.5 11.5647V6.37905C1.5 5.96482 1.83579 5.62905 2.25 5.62905H4.42444C4.42444 5.62905 6.375 2.25 9 2.25Z"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinejoin="round"
    />
    <path
      d="M12 5.625C12.2337 5.83369 12.4455 6.06739 12.6315 6.32205C13.177 7.0689 13.5 7.99586 13.5 9C13.5 9.99544 13.1826 10.915 12.6457 11.6585C12.4563 11.9207 12.2397 12.161 12 12.375"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.8384 15.4447C15.0313 14.1358 16.4999 11.7394 16.4999 9.00004C16.4999 6.30323 15.0765 3.93885 12.94 2.61719"
      stroke="#fff"
      strokeWidth="1.2"
      strokeLinecap="round"
    />
  </svg>
)

const IconFullScreen = () => (
  <svg width="20" height="20" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.8317 9.19826H13.6673L13.6089 13.6668H9.22668V11.8344L11.8304 11.8332V9.19826H11.8317ZM2.16584 9.19826V11.8332H4.77081V13.6668H0.333984V9.19826H2.16584ZM4.77081 0.333496V2.16588H2.16584V4.80207H0.333984V0.333496H4.77081ZM13.6635 0.333496V4.80207H11.8317L11.8304 2.16588H9.22668V0.333496H13.6635Z"
      fill="white"
    />
  </svg>
)

const IconExitFullScreen = () => (
  <svg width="48" height="48" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" rx="16" fill="black" fillOpacity="0.4" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.5619 14.1997H9.33203L9.33203 12.1908L12.4173 12.1894L12.4173 9.33398L14.5619 9.33398V14.1997ZM17.4733 17.7663V22.6668H19.6173L19.6188 19.7758H22.6661V17.7663H17.4733Z"
      fill="white"
    />
  </svg>
)

interface VideoPlayerProps {
  videoUrl?: string
  posterUrl: string
  withMute?: boolean // 是否显示静音按钮
  initialMuted?: boolean // 初始静音状态
  onVideoRef?: (ref: HTMLVideoElement | null) => void // 视频引用回调
}

const VideoPlayer = ({
  videoUrl,
  posterUrl,
  withMute = true,
  initialMuted = true,
  onVideoRef,
}: VideoPlayerProps) => {
  const [videoLoading, setVideoLoading] = useState(true)
  const [videoError, setVideoError] = useState(false)
  // 若初始为静音，则尝试自动播放（Safari 仅允许静音自动播放）
  const [videoPaused, setVideoPaused] = useState(() => !initialMuted)
  const [isMuted, setIsMuted] = useState(initialMuted)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isClient, setIsClient] = useState(false) // 添加客户端检测

  const playerRef = useRef<ReactPlayer>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const secureVideoUrl = videoUrl
  const securePosterUrl = posterUrl

  // 处理视频播放/暂停切换
  const handleToggleVideoPlay = () => {
    if (!playerRef.current) return

    const internalPlayer = playerRef.current.getInternalPlayer() as HTMLVideoElement
    if (!internalPlayer) return

    if (internalPlayer.paused) {
      console.log('播放视频')
      internalPlayer.play().catch((error) => {
        console.log('视频播放失败:', error)
        setVideoError(true)
        setVideoPaused(true) // 播放失败时保持暂停状态
      })
      setVideoPaused(false) // 手动更新状态
    } else {
      console.log('暂停视频')
      internalPlayer.pause()
      setVideoPaused(true) // 手动更新状态
    }
  }

  // 客户端检测
  useEffect(() => {
    setIsClient(true)
  }, [])

  // 监听视频元素的状态并同步到组件状态
  useEffect(() => {
    if (!isClient || !playerRef.current) return

    const internalPlayer = playerRef.current.getInternalPlayer() as HTMLVideoElement
    if (!internalPlayer) return

    // 传递视频引用给父组件
    onVideoRef?.(internalPlayer)

    // 创建一个函数来同步视频状态
    const syncVideoState = () => {
      if (internalPlayer.paused) {
        console.log('视频处于暂停状态')
        setVideoPaused(true)
      } else {
        console.log('视频处于播放状态')
        setVideoPaused(false)
      }
    }

    // 监听视频的各种状态变化
    internalPlayer.addEventListener('pause', syncVideoState)
    internalPlayer.addEventListener('play', syncVideoState)
    internalPlayer.addEventListener('canplay', syncVideoState)
    internalPlayer.addEventListener('loadeddata', syncVideoState)

    // 初始同步一次
    syncVideoState()

    // 增加一个延时检查，确保状态正确同步
    const timer = setTimeout(() => {
      syncVideoState()
    }, 300)

    return () => {
      // 清理监听器
      internalPlayer.removeEventListener('pause', syncVideoState)
      internalPlayer.removeEventListener('play', syncVideoState)
      internalPlayer.removeEventListener('canplay', syncVideoState)
      internalPlayer.removeEventListener('loadeddata', syncVideoState)
      clearTimeout(timer)
      // 清理时传递 null 给父组件
      onVideoRef?.(null)
    }
  }, [onVideoRef, secureVideoUrl, isClient])

  // 降级：若静音自动播放被 Safari 拒绝，则在用户首次交互时再尝试播放
  useEffect(() => {
    if (!isClient || !playerRef.current || !containerRef.current) return

    const internal = playerRef.current.getInternalPlayer() as HTMLVideoElement | null
    if (!internal) return

    // 仅在静音时尝试自动播放，符合 Safari 策略
    if (!isMuted) return

    const el = containerRef.current

    const handler: EventListener = () => {
      if (!internal.paused) return
      // 确保静音后再尝试
      internal.muted = true
      setIsMuted(true)
      internal
        .play()
        .then(() => {
          setVideoPaused(false)
          setVideoLoading(false)
        })
        .catch(() => {
          // 忽略，仍由用户手动控制
        })
    }

    // 监听一次用户首次交互
    const oncePassiveOptions: AddEventListenerOptions = { once: true, passive: true }
    const onceOptions: AddEventListenerOptions = { once: true }
    el.addEventListener('touchstart', handler, oncePassiveOptions)
    el.addEventListener('click', handler, onceOptions)

    return () => {
      el.removeEventListener('touchstart', handler)
      el.removeEventListener('click', handler)
    }
  }, [isClient, isMuted, secureVideoUrl])

  // ReactPlayer事件处理
  const handlePlayerReady = () => {
    console.log('ReactPlayer ready')
    // 就绪即取消 loading，播放由受控属性 playing 决定，避免与手动 play 冲突
    setVideoLoading(false)
  }

  const handlePlayerError = (error: unknown) => {
    console.error('ReactPlayer error:', error)
    setVideoError(true)
    setVideoLoading(false)
  }

  const handlePlayerPlay = () => {
    console.log('ReactPlayer playing')
    setVideoPaused(false)
    setVideoLoading(false)
  }

  const handlePlayerPause = () => {
    console.log('ReactPlayer paused')
    setVideoPaused(true)
  }

  // 处理静音切换
  const handleToggleMute = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!playerRef.current) return

    const internalPlayer = playerRef.current.getInternalPlayer() as HTMLVideoElement
    if (!internalPlayer) return

    const nextMuted = !isMuted
    setIsMuted(nextMuted)
    internalPlayer.muted = nextMuted
  }

  // 处理全屏切换
  const handleToggleFullscreen = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!containerRef.current) return

    if (!isFullscreen) {
      // 进入全屏
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen()
      } else if (containerRef.current.webkitRequestFullscreen) {
        containerRef.current.webkitRequestFullscreen()
      } else if (containerRef.current.msRequestFullscreen) {
        containerRef.current.msRequestFullscreen()
      }
    } else {
      // 退出全屏
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    }
  }

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('msfullscreenchange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('msfullscreenchange', handleFullscreenChange)
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className={`relative mx-auto bg-[#0F0F0F] ${
        isFullscreen ? 'fixed inset-0 z-50' : 'responsive-product-gallery aspect-square'
      }`}
      style={{ borderRadius: '20px', overflow: 'hidden' }}>
      {/* 加载状态 */}
      {videoLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      )}

      {/* 错误提示 */}
      {videoError && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-500">
          视频加载失败
        </div>
      )}

      {/* 视频播放控制层 - 只覆盖视频播放区域 */}
      <div
        className="absolute left-0 right-0 top-0 z-30 cursor-pointer"
        style={{
          bottom: '50px' /* 保留底部空间给控制条 */,
        }}
        onClick={handleToggleVideoPlay}>
        {/* 暂停状态时显示播放图标 */}
        {videoPaused && !videoLoading && !videoError && (
          <div className="flex h-full w-full items-center justify-center">
            <Play size={48} />
          </div>
        )}
      </div>

      {/* 控制按钮容器 */}
      <div className="absolute bottom-8 left-8 right-8 z-40 flex items-center justify-between">
        {/* 静音控制按钮 */}
        {withMute && (
          <button
            className="rounded-full bg-black bg-opacity-50 p-2 text-white"
            onClick={handleToggleMute}
            aria-label={isMuted ? '取消静音' : '静音'}>
            {isMuted ? <IconVolumeMute /> : <IconVolumeUnmute />}
          </button>
        )}

        {/* 全屏控制按钮 */}
        <button
          className="rounded-full bg-black bg-opacity-50 p-2 text-white"
          onClick={handleToggleFullscreen}
          aria-label={isFullscreen ? '退出全屏' : '全屏'}>
          {isFullscreen ? <IconExitFullScreen /> : <IconFullScreen />}
        </button>
      </div>

      {isClient ? (
        <ReactPlayer
          ref={playerRef}
          url={secureVideoUrl}
          className="custom-video-player"
          style={{
            aspectRatio: isFullscreen ? 'auto' : '1/1',
            width: '100%',
            height: '100%',
            position: 'relative',
            zIndex: 20,
          }}
          width="100%"
          height="100%"
          playing={!videoPaused}
          muted={isMuted}
          loop
          playsinline
          controls={false}
          onReady={handlePlayerReady}
          onError={handlePlayerError}
          onPlay={handlePlayerPlay}
          onPause={handlePlayerPause}
          onBuffer={() => setVideoLoading(true)}
          onBufferEnd={() => setVideoLoading(false)}
          config={{
            file: {
              attributes: {
                poster: securePosterUrl,
                preload: 'metadata',
                // 明确设置多种内联播放与静音属性，适配 iOS Safari
                muted: isMuted,
                playsInline: true,
                playsinline: true,
                'webkit-playsinline': 'true',
                'x5-playsinline': 'true',
              },
              hlsOptions: {
                maxBufferLength: 30,
                maxBufferSize: 60 * 1000 * 1000, // 60MB
                enableWorker: true,
              },
            },
          }}
        />
      ) : (
        // 服务端渲染时显示占位符
        <div
          className="custom-video-player flex h-full w-full items-center justify-center bg-black object-contain"
          style={{
            aspectRatio: isFullscreen ? 'auto' : '1/1',
            width: '100%',
            height: '100%',
            position: 'relative',
            zIndex: 20,
            backgroundImage: `url(${securePosterUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}>
          {/* 播放按钮占位符 */}
          <div className="flex items-center justify-center">
            <Play size={48} />
          </div>
        </div>
      )}
    </div>
  )
}

export default VideoPlayer
