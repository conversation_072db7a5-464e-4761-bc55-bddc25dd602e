'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import Image from 'next/image'
import { Splide, SplideSlide } from '@splidejs/react-splide'
import { cn, RelationItem, TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { But<PERSON> } from 'antd'
import clsx from 'clsx'
import ReactPlayer from 'react-player'

import { IconArrow } from '@/components'
import { Arrow } from '@/components'
import { Link } from '@/i18n/navigation'

import styles from './index.module.css'

import '@splidejs/react-splide/css'

type BannerProps = {
  bannerData: RelationItem[] | null
}

interface CarouselProps {
  go: (index: number | string) => void
  splide?: {
    options: Record<string, unknown>
    refresh: () => void
  }
}

const Banner = ({ bannerData }: BannerProps) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const carouselRef = useRef<CarouselProps>(null)
  const videoListRef = useRef<HTMLVideoElement[]>([])
  const [bannerIndex, setBannerIndex] = useState(0)
  // 第一个数据为视频的时候，等它加载完
  const [firstVideoLoad, setFirstVideoLoad] = useState(false)
  const bannerTimer = useRef<NodeJS.Timeout>()
  const uuid = useRef(0)
  const [isClient, setIsClient] = useState(false) // 添加客户端检测
  const { reportEvent } = useVolcAnalytics()

  const bannerDataFiltered = useMemo(() => {
    return bannerData
      ?.filter((item) => item?.button_url?.type !== 'seckill')
      .filter((item) => !!item?.image_url_desktop)
  }, [bannerData])

  const bannerTotal = useMemo(() => {
    return bannerDataFiltered?.length || 0
  }, [bannerDataFiltered])

  const playDotsAnimation = useCallback((index: number) => {
    const elems = document.querySelectorAll('.progressLine')
    const len = elems.length
    const r = document.querySelectorAll('.progressLine')[index] as HTMLElement
    let n: number | undefined
    let O = 5000

    if (!r) {
      return
    }

    window.cancelAnimationFrame(uuid.current)

    // 处理视频的播放
    if (videoListRef.current[index]) {
      const video = videoListRef.current[index]
      // 检查视频是否已经准备好播放
      if (video.readyState >= 2) {
        // HAVE_ENOUGH_DATA
        video.play().catch((e) => console.log('Video play failed:', e))
        O = video.duration || 5000
      } else {
        // 如果视频还没准备好，等待5秒后重试
        setTimeout(() => {
          if (video.readyState >= 2) {
            video.play().catch((e) => console.log('Video play failed:', e))
          }
        }, 1000)
      }
    }

    if (!r && len > 1) {
      return
    }
    const s = (Number.isNaN(parseInt(r.style.width)) ? 0 : parseInt(r.style.width)) / 100
    uuid.current = window.requestAnimationFrame(function e(t) {
      if (n === undefined && n !== 0) {
        n = t - O * s
      }
      let a = t - n
      if (videoListRef.current[index] && videoListRef.current[index].readyState >= 2) {
        a = videoListRef.current[index].currentTime
      }
      if (a < O) {
        const o = Math.min((a / O) * 100, 100)
        r.style.width = `${o}%`
        elems.forEach((item, j) => {
          if (index > j) {
            const i = item as HTMLElement
            i.style.width = '100%'
          }
        })
        uuid.current = window.requestAnimationFrame(e)
      } else {
        if (index === len - 1) {
          elems.forEach((item) => {
            const i = item as HTMLElement
            i.style.width = '0%'
          })
        }
        carouselRef.current?.go('>')
      }
    })
  }, [])

  const handleNext = () => {
    stopDotsAnimation()
    let index = bannerIndex + 1
    if (index > bannerTotal - 1) {
      index = 0
    }
    handleClickDot(index)
  }

  const handlePrev = () => {
    let index = bannerIndex - 1
    if (index < 0) {
      index = bannerTotal - 1
    }
    handleClickDot(index)
  }

  const stopDotsAnimation = useCallback(() => {
    window.cancelAnimationFrame(uuid.current)
    if (videoListRef.current[activeIndex]) {
      videoListRef.current[activeIndex].pause()
    }
  }, [activeIndex])

  const handleClickDot = useCallback(
    (index: number) => {
      if (index === bannerIndex) return
      stopDotsAnimation()
      const elems = document.querySelectorAll('.progressLine')
      elems.forEach((item, j) => {
        const i = item as HTMLElement
        if (index > j) {
          i.style.width = '100%'
        } else {
          i.style.width = '0%'
        }
      })

      carouselRef.current?.go(index)
    },
    [bannerIndex, stopDotsAnimation],
  )

  const handleSideChange = (index: number) => {
    setActiveIndex(index)
    // 视频进度重置为0，只处理可见的视频
    videoListRef.current.forEach((item, i) => {
      if (item) {
        if (i === index) {
          // 当前视频重新播放
          item.currentTime = 0
          item.play().catch((e) => console.log('Video play failed:', e))
        } else {
          // 其他视频暂停并重置
          item.pause()
          item.currentTime = 0
        }
      }
    })
    playDotsAnimation(index)
  }

  // 客户端检测
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Banner曝光埋点
  useEffect(() => {
    if (bannerDataFiltered && bannerDataFiltered.length > 0) {
      bannerDataFiltered.forEach((item) => {
        // 上报banner图片曝光事件
        reportEvent(TRACK_EVENT.shop_homepage_banner_picture_exposure, {
          banner_id: item?.id || '',
          banner_name: item?.title || '',
        })

        // 上报top banner图片曝光事件
        reportEvent(TRACK_EVENT.shop_homepage_top_banner_picture_exposure, {
          banner_id: item?.id || '',
          banner_name: item?.title || '',
        })
      })
    }
  }, [bannerDataFiltered, reportEvent])

  useEffect(() => {
    if (!bannerDataFiltered?.[0]?.video_url) {
      setFirstVideoLoad(true)
      playDotsAnimation(0)
    }
  }, [bannerDataFiltered, playDotsAnimation])

  // 添加视频加载错误处理
  useEffect(() => {
    const handleVideoError = (video: HTMLVideoElement, index: number) => {
      console.log(`Video ${index} failed to load:`, video.error)
      // 如果视频加载失败，自动切换到下一个
      if (index === activeIndex) {
        setTimeout(() => {
          const nextIndex = (index + 1) % (bannerDataFiltered?.length || 1)
          handleClickDot(nextIndex)
        }, 1000)
      }
    }

    const currentVideoList = videoListRef.current

    currentVideoList.forEach((video, index) => {
      if (video) {
        video.addEventListener('error', () => handleVideoError(video, index))
      }
    })

    return () => {
      currentVideoList.forEach((video, index) => {
        if (video) {
          video.removeEventListener('error', () => handleVideoError(video, index))
        }
      })
    }
  }, [activeIndex, bannerDataFiltered, handleClickDot])

  useEffect(() => {
    if (bannerTimer.current) {
      return
    }

    if (firstVideoLoad) {
      bannerTimer.current = setInterval(() => {
        if (carouselRef.current) {
          clearInterval(bannerTimer.current)
          playDotsAnimation(0)
        }
      }, 1000)
    }
  }, [playDotsAnimation, firstVideoLoad])

  const handleBeforeChange = (from: number, to: number) => {
    // 确保索引在有效范围内
    if (to >= 0 && (!bannerDataFiltered || to < bannerDataFiltered?.length)) {
      handleClickDot(to)
    }
  }
  const setRef = (index: number) => (el: HTMLVideoElement) => {
    videoListRef.current[index] = el
  }

  const renderVideo = (item: RelationItem, index: number) => {
    const isActive = index === activeIndex
    const shouldPreload =
      index === activeIndex || index === (activeIndex + 1) % (bannerDataFiltered?.length || 1)

    return (
      <div className="absolute inset-0 h-full w-full overflow-hidden">
        {isClient ? (
          <ReactPlayer
            src={item?.video_url || ''}
            controls={false}
            ref={setRef(index)}
            playsInline
            muted={true}
            width="100%"
            height="100%"
            playing={isActive}
            preload={shouldPreload ? 'auto' : 'metadata'}
            wrapper="div"
            style={{
              objectFit: 'cover',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
            onEnded={() => {
              const video = videoListRef.current[index]
              if (video) {
                video.currentTime = 0
                // 视频播放完成，设置当前进度条为100%
                const progressLine = document.querySelectorAll('.progressLine')[
                  index
                ] as HTMLElement
                if (progressLine) {
                  progressLine.style.width = '100%'
                }

                // 添加防抖，避免立即触发多次切换
                if (index === activeIndex) {
                  // 使用setTimeout避免与其他切换逻辑冲突
                  setTimeout(() => {
                    if (index === activeIndex) {
                      // 检查是否是最后一个索引
                      const nextIndex = (index + 1) % (bannerDataFiltered?.length || 1)
                      if (nextIndex === 0 && index === (bannerDataFiltered?.length || 1) - 1) {
                        // 如果是从最后一个切换到第一个，特殊处理
                        setActiveIndex(0) // 先更新状态
                      }
                      handleBeforeChange(index, nextIndex)
                    }
                  }, 100)
                }
              }
            }}
            onReady={() => {
              // 视频准备好后，如果是第一个视频且需要加载
              if (index === 0 && !firstVideoLoad) {
                setFirstVideoLoad(true)
              }
            }}
          />
        ) : (
          // 服务端渲染时显示占位符
          <div
            className="absolute inset-0 h-full w-full"
            style={{
              objectFit: 'cover',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
          />
        )}
      </div>
    )
  }

  const getBannerHeight = () => {
    // 根据屏幕宽度计算Banner高度
    const width = window.innerWidth
    if (width >= 1920) {
      return 800
    } else if (width >= 1440) {
      const height = 597 + ((width - 1440) / 480) * 203
      return Math.round(height)
    } else if (width >= 1024) {
      // 响应式计算高度 - 与CSS中的clamp函数保持一致
      const height = 424 + ((width - 1024) / 416) * 173
      return Math.round(height)
    } else {
      return 424
    }
  }

  const [bannerWidth, setBannerWidth] = useState(1024)

  // 添加窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      const realWidth = Math.max(document.documentElement.clientWidth, 1024)
      setBannerWidth(realWidth)
      if (carouselRef.current?.splide) {
        carouselRef.current.splide.options = {
          ...carouselRef.current.splide.options,
          height: getBannerHeight(),
          width: realWidth,
        }
        carouselRef.current.splide.refresh()
      }
    }

    window.addEventListener('resize', handleResize)
    // 初始化时也执行一次
    handleResize()

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <div
      className="group relative mx-auto w-full max-w-[2560px] overflow-hidden"
      style={{
        width: bannerWidth,
        height: getBannerHeight(),
      }}>
      <Splide
        ref={carouselRef}
        options={{
          type: 'loop',
          pagination: !1,
          drag: !1,
          arrows: !1,
          rewind: !0,
          perMove: 1,
          preloadPages: 2,
          interval: 5000,
          height: getBannerHeight(), // 使用动态计算的高度
          width: '100%',
        }}
        onMove={(_e: object, a: number) => {
          stopDotsAnimation()
          setBannerIndex(a)
          handleSideChange(a)
        }}>
        {bannerDataFiltered?.map((item, index) => (
          <SplideSlide
            key={index}
            onMouseEnter={() => {
              stopDotsAnimation()
            }}
            onMouseLeave={() => {
              playDotsAnimation(index)
            }}>
            <Link
              className="relative block h-full w-full"
              href={item?.button_url?.url || '#'}
              onClick={(e) => {
                if (!item?.button_url?.url) {
                  e.preventDefault()
                  return
                }

                // 上报banner图片点击事件
                reportEvent(TRACK_EVENT.shop_homepage_banner_picture_click, {
                  banner_id: item?.id || '',
                  banner_name: item?.title || '',
                })

                // 上报top banner图片点击事件
                reportEvent(TRACK_EVENT.shop_homepage_top_banner_picture_click, {
                  banner_id: item?.id || '',
                  banner_name: item?.title || '',
                })
              }}>
              <div className="absolute inset-0 flex h-full w-full flex-col items-center overflow-hidden">
                {item?.video_url ? (
                  renderVideo(item, index)
                ) : (
                  <div className="absolute inset-0 h-full w-full overflow-hidden">
                    <Image
                      src={item?.image_url_desktop || ''}
                      alt={item?.title || ''}
                      fill
                      className="object-cover"
                      priority
                      sizes="100vw"
                      style={{ objectPosition: 'center center' }}
                    />
                  </div>
                )}

                {/* 内容区域 */}
                <div className="relative mx-auto">
                  <div className="flex flex-col items-center pt-[66.5px] text-white">
                    {item?.subtitle && (
                      <div className="mb-[8px] truncate text-center font-miSansMedium380 text-[18px] leading-[120%] 2xl:text-[20px]">
                        {item?.subtitle}
                      </div>
                    )}
                    {item?.title && (
                      <div className="mb-[8px] truncate text-center font-miSansSemibold520 text-[40px] leading-[120%] 2xl:text-[64px]">
                        {item?.title}
                      </div>
                    )}
                    {item?.description && (
                      <div className="mb-[24px] truncate text-center font-miSansMedium380 text-[16px] leading-[140%] 2xl:text-[24px]">
                        {item?.description}
                      </div>
                    )}
                    {item?.button_text && (
                      <Button
                        className={clsx(
                          'min-w-[146px] font-miSansDemiBold450 text-[16px] leading-[20px]',
                          item?.button_arrow ? 'justify-between' : 'justify-center',
                        )}
                        type="primary"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()

                          // 上报查看详情按钮点击事件
                          reportEvent(TRACK_EVENT.shop_homepage_top_banner_detail_button_click, {
                            banner_id: item?.id || '',
                            banner_name: item?.title || '',
                          })
                        }}>
                        {item?.button_text}
                        {item.button_arrow && <IconArrow color="#fff" size={20} rotate={-90} />}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          </SplideSlide>
        ))}
      </Splide>

      {/* 选项卡 */}
      {bannerTotal > 1 && (
        <>
          <div className="absolute bottom-0 left-0 h-[80px] w-full bg-[linear-gradient(360deg,rgba(0,0,0,0.5)_-71.82%,rgba(0,0,0,0)_88.64%)] xll:h-[106px]">
            <div className="max-container-no-mb">
              <div className="mx-auto flex w-fit justify-center">
                <div className="flex h-fit items-center justify-center text-[16px] text-white">
                  {bannerDataFiltered?.map((item, index) => (
                    <div
                      key={index}
                      className={cn(
                        'relative flex cursor-pointer items-center justify-center border-t border-gray-3 px-[20px] pt-[11px]',
                      )}
                      onClick={() => {
                        handleClickDot(index)
                      }}>
                      {/* activeprogressbar: 图片进度条动画 */}
                      <div
                        className={`progressLine absolute left-0 top-[-1px] h-[1px] w-0 bg-white ${activeIndex === index ? styles.activeprogressbar : ''}`}></div>

                      <span
                        className={cn(
                          'relative flex h-[54px] items-center whitespace-nowrap px-[5px] text-[16px] leading-[22px] text-white',
                          activeIndex === index
                            ? '!text-white before:font-miSansSemibold520 before:!text-white before:!text-opacity-100'
                            : '!text-opacity-60',
                          {
                            'invisible before:visible before:absolute before:inset-0 before:flex before:items-center before:justify-center before:content-[attr(data-value)]':
                              activeIndex === index,
                          },
                        )}
                        data-value={item?.bottom_title?.trim() || ''}>
                        {item?.bottom_title?.trim() || ''}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 top-0 ml-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handlePrev}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow rotate={180} color="currentColor" />
              </button>
            </div>
          </div>
          <div className="absolute bottom-0 right-0 top-0 mr-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handleNext}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow color="currentColor" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Banner
